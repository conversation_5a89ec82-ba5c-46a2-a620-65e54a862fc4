<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - Waste Management</title>
    <link rel="stylesheet" href="/styles/styles.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Waste Management System - Test Page</h1>
        
        <!-- Test Tailwind CSS -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Tailwind CSS Test</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-500 text-white p-4 rounded">Blue Card</div>
                <div class="bg-green-500 text-white p-4 rounded">Green Card</div>
                <div class="bg-red-500 text-white p-4 rounded">Red Card</div>
            </div>
            <p class="mt-4 text-gray-600">If you can see styled cards above, Tailwind CSS is working!</p>
        </div>

        <!-- Test Data Loading -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Data Loading Test</h2>
            <button id="testDataBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                Test Data Loading
            </button>
            <div id="dataResults" class="mt-4 p-4 bg-gray-50 rounded hidden">
                <h3 class="font-medium mb-2">Results:</h3>
                <div id="dataContent"></div>
            </div>
        </div>

        <!-- Test Authentication -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Authentication Test</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="testLogin('admin', 'admin123', 'municipal_officer')" 
                        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded">
                    Test Admin Login
                </button>
                <button onclick="testLogin('driver1', 'drive123', 'driver')" 
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded">
                    Test Driver Login
                </button>
                <button onclick="testLogin('user1', 'user123', 'citizen')" 
                        class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded">
                    Test Citizen Login
                </button>
            </div>
            <div id="authResults" class="mt-4 p-4 bg-gray-50 rounded hidden">
                <h3 class="font-medium mb-2">Authentication Results:</h3>
                <div id="authContent"></div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Navigation</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="/" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-center">
                    Login Page
                </a>
                <a href="/admin-dashboard.html" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-center">
                    Admin Dashboard
                </a>
                <a href="/driver-dashboard.html" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-center">
                    Driver Dashboard
                </a>
                <a href="/citizen-dashboard.html" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded text-center">
                    Citizen Dashboard
                </a>
            </div>
        </div>
    </div>

    <script type="module">
        import { AuthManager } from './js/auth.js';

        // Initialize auth manager
        const authManager = new AuthManager();
        window.authManager = authManager;

        // Test data loading
        document.getElementById('testDataBtn').addEventListener('click', async () => {
            const resultsDiv = document.getElementById('dataResults');
            const contentDiv = document.getElementById('dataContent');
            
            resultsDiv.classList.remove('hidden');
            contentDiv.innerHTML = 'Loading...';

            try {
                const [usersRes, routesRes, complaintsRes] = await Promise.all([
                    fetch('./data/users.json'),
                    fetch('./data/routes.json'),
                    fetch('./data/complaints.json')
                ]);

                const users = await usersRes.json();
                const routes = await routesRes.json();
                const complaints = await complaintsRes.json();

                contentDiv.innerHTML = `
                    <div class="space-y-2">
                        <p class="text-green-600">✓ Users loaded: ${users.municipal_officers?.length || 0} officers, ${users.drivers?.length || 0} drivers, ${users.citizens?.length || 0} citizens</p>
                        <p class="text-green-600">✓ Routes loaded: ${routes.routes?.length || 0} routes, ${routes.vehicles?.length || 0} vehicles</p>
                        <p class="text-green-600">✓ Complaints loaded: ${complaints.complaints?.length || 0} complaints, ${complaints.reports?.length || 0} reports</p>
                    </div>
                `;
            } catch (error) {
                contentDiv.innerHTML = `<p class="text-red-600">✗ Error: ${error.message}</p>`;
            }
        });

        // Test authentication function
        window.testLogin = async (username, password, role) => {
            const resultsDiv = document.getElementById('authResults');
            const contentDiv = document.getElementById('authContent');
            
            resultsDiv.classList.remove('hidden');
            contentDiv.innerHTML = 'Testing login...';

            try {
                const result = await authManager.login(username, password, role);
                
                if (result.success) {
                    contentDiv.innerHTML = `
                        <div class="space-y-2">
                            <p class="text-green-600">✓ Login successful!</p>
                            <p><strong>User:</strong> ${result.user.name}</p>
                            <p><strong>Role:</strong> ${result.user.role}</p>
                            <p><strong>Email:</strong> ${result.user.email}</p>
                        </div>
                    `;
                    
                    // Auto logout after 3 seconds
                    setTimeout(() => {
                        authManager.logout();
                        contentDiv.innerHTML += '<p class="text-blue-600 mt-2">Auto-logged out for testing</p>';
                    }, 3000);
                } else {
                    contentDiv.innerHTML = `<p class="text-red-600">✗ Login failed: ${result.message}</p>`;
                }
            } catch (error) {
                contentDiv.innerHTML = `<p class="text-red-600">✗ Error: ${error.message}</p>`;
            }
        };
    </script>
</body>
</html>
