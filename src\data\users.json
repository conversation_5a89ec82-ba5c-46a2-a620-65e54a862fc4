{"municipal_officers": [{"id": "admin001", "username": "admin", "password": "admin123", "name": "<PERSON>", "email": "<EMAIL>", "role": "municipal_officer", "permissions": ["view_all", "manage_drivers", "manage_routes", "view_analytics", "resolve_complaints"], "active": true, "created_at": "2024-01-01T00:00:00Z"}, {"id": "admin002", "username": "supervisor", "password": "super123", "name": "<PERSON>", "email": "<EMAIL>", "role": "municipal_officer", "permissions": ["view_all", "manage_drivers", "view_analytics"], "active": true, "created_at": "2024-01-15T00:00:00Z"}], "drivers": [{"id": "driver001", "username": "driver1", "password": "drive123", "name": "<PERSON>", "email": "<EMAIL>", "role": "driver", "vehicle_id": "TRUCK001", "license_number": "DL123456789", "phone": "+**********", "active": true, "created_at": "2024-01-10T00:00:00Z"}, {"id": "driver002", "username": "driver2", "password": "drive456", "name": "<PERSON>", "email": "<EMAIL>", "role": "driver", "vehicle_id": "TRUCK002", "license_number": "DL987654321", "phone": "+**********", "active": true, "created_at": "2024-01-12T00:00:00Z"}], "citizens": [{"id": "citizen001", "username": "user1", "password": "user123", "name": "<PERSON>", "email": "<EMAIL>", "role": "citizen", "address": "123 Main St, City, State 12345", "phone": "+**********", "bin_id": "BIN001", "active": true, "created_at": "2024-01-20T00:00:00Z"}, {"id": "citizen002", "username": "user2", "password": "user456", "name": "<PERSON>", "email": "<EMAIL>", "role": "citizen", "address": "456 Oak Ave, City, State 12345", "phone": "+**********", "bin_id": "BIN002", "active": true, "created_at": "2024-01-22T00:00:00Z"}]}