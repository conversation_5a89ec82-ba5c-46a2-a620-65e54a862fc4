// Test script to verify Tailwind CSS is working
const http = require('http');

async function testTailwindCSS() {
    console.log('Testing Tailwind CSS integration...\n');
    
    try {
        // For this test, we'll use a simple HTTP request to check if CSS is being processed
        
        // Test if the main page loads
        const testPage = (url, description) => {
            return new Promise((resolve, reject) => {
                const request = http.get(url, (response) => {
                    let data = '';
                    
                    response.on('data', (chunk) => {
                        data += chunk;
                    });
                    
                    response.on('end', () => {
                        // Check if Tailwind classes are present in the HTML
                        const hasTailwindClasses = data.includes('bg-gray-50') || 
                                                 data.includes('text-white') || 
                                                 data.includes('px-4') ||
                                                 data.includes('py-2');
                        
                        // Check if the CSS file is being imported
                        const hasCSSImport = data.includes('/styles/styles.css') || 
                                           data.includes('styles.css');
                        
                        console.log(`✓ ${description}: Status ${response.statusCode}`);
                        console.log(`  - Tailwind classes found: ${hasTailwindClasses ? '✓' : '✗'}`);
                        console.log(`  - CSS import found: ${hasCSSImport ? '✓' : '✗'}`);
                        
                        resolve({ 
                            status: response.statusCode, 
                            hasTailwindClasses, 
                            hasCSSImport,
                            data 
                        });
                    });
                });
                
                request.on('error', (error) => {
                    console.log(`✗ ${description}: ${error.message}`);
                    reject(error);
                });
                
                request.setTimeout(5000, () => {
                    console.log(`✗ ${description}: Timeout`);
                    request.destroy();
                    reject(new Error('Timeout'));
                });
            });
        };
        
        // Test CSS file directly
        const testCSS = (url, description) => {
            return new Promise((resolve, reject) => {
                const request = http.get(url, (response) => {
                    let data = '';
                    
                    response.on('data', (chunk) => {
                        data += chunk;
                    });
                    
                    response.on('end', () => {
                        // Check if Tailwind base styles are present
                        const hasTailwindBase = data.includes('@tailwind') || 
                                              data.includes('--tw-') ||
                                              data.includes('.bg-gray-50') ||
                                              data.includes('.text-white');
                        
                        console.log(`✓ ${description}: Status ${response.statusCode}`);
                        console.log(`  - Tailwind CSS processed: ${hasTailwindBase ? '✓' : '✗'}`);
                        console.log(`  - CSS size: ${data.length} bytes`);
                        
                        resolve({ 
                            status: response.statusCode, 
                            hasTailwindBase,
                            size: data.length,
                            data: data.substring(0, 200) + '...' // First 200 chars
                        });
                    });
                });
                
                request.on('error', (error) => {
                    console.log(`✗ ${description}: ${error.message}`);
                    reject(error);
                });
                
                request.setTimeout(5000, () => {
                    console.log(`✗ ${description}: Timeout`);
                    request.destroy();
                    reject(new Error('Timeout'));
                });
            });
        };
        
        const tests = [
            { url: 'http://localhost:5173/', description: 'Main Login Page' },
            { url: 'http://localhost:5173/test.html', description: 'Test Page' },
        ];
        
        let passed = 0;
        let failed = 0;
        
        for (const test of tests) {
            try {
                const result = await testPage(test.url, test.description);
                if (result.hasTailwindClasses && result.hasCSSImport) {
                    passed++;
                } else {
                    failed++;
                }
                console.log(''); // Empty line for readability
            } catch (error) {
                failed++;
                console.log(''); // Empty line for readability
            }
        }
        
        // Test CSS file
        try {
            console.log('Testing CSS file directly...');
            const cssResult = await testCSS('http://localhost:5173/styles/styles.css', 'Styles CSS File');
            console.log(`CSS Preview: ${cssResult.data}`);
            console.log('');
        } catch (error) {
            console.log('CSS file test failed:', error.message);
            console.log('');
        }
        
        console.log(`\nTest Results:`);
        console.log(`✓ Passed: ${passed}`);
        console.log(`✗ Failed: ${failed}`);
        console.log(`Total: ${passed + failed}`);
        
        if (failed === 0) {
            console.log('\n🎉 Tailwind CSS appears to be working correctly!');
        } else {
            console.log('\n⚠️  Some issues detected with Tailwind CSS integration.');
        }
        
    } catch (error) {
        console.error('Test failed:', error);
    }
}

testTailwindCSS();
