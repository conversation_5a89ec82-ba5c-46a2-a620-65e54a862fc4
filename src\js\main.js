import '../styles/styles.css';
import { AuthManager, redirectToDashboard, checkAuthAndRedirect } from './auth.js';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already logged in and redirect
    checkAuthAndRedirect();

    // Initialize login functionality
    initializeLogin();
});

function initializeLogin() {
    const roleButtons = document.querySelectorAll('.role-btn');
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const errorMessage = document.getElementById('errorMessage');
    let selectedRole = null;

    // Role selection handling
    roleButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active state from all buttons
            roleButtons.forEach(btn => {
                btn.classList.remove('ring-2', 'ring-offset-2');
                btn.classList.add('border-2');
            });

            // Add active state to selected button
            this.classList.add('ring-2', 'ring-offset-2');
            this.classList.remove('border-2');

            selectedRole = this.dataset.role;

            // Enable login button if role is selected
            updateLoginButton();
        });
    });

    // Form input handling
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');

    [usernameInput, passwordInput].forEach(input => {
        input.addEventListener('input', updateLoginButton);
    });

    function updateLoginButton() {
        const hasCredentials = usernameInput.value.trim() && passwordInput.value.trim();
        const hasRole = selectedRole !== null;

        if (hasCredentials && hasRole) {
            loginBtn.disabled = false;
            loginBtn.classList.remove('bg-gray-400');
            loginBtn.classList.add('bg-indigo-600', 'hover:bg-indigo-700');
        } else {
            loginBtn.disabled = true;
            loginBtn.classList.add('bg-gray-400');
            loginBtn.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
        }
    }

    // Login form submission
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        if (!selectedRole) {
            showError('Please select your role');
            return;
        }

        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        if (!username || !password) {
            showError('Please enter both username and password');
            return;
        }

        // Show loading state
        loginBtn.disabled = true;
        loginBtn.textContent = 'Signing in...';
        hideError();

        try {
            const result = await window.authManager.login(username, password, selectedRole);

            if (result.success) {
                // Redirect to appropriate dashboard
                redirectToDashboard(result.user.role);
            } else {
                showError(result.message);
            }
        } catch (error) {
            console.error('Login error:', error);
            showError('An error occurred during login. Please try again.');
        } finally {
            // Reset button state
            loginBtn.disabled = false;
            loginBtn.textContent = 'Sign in';
            updateLoginButton();
        }
    });

    function showError(message) {
        errorMessage.querySelector('span').textContent = message;
        errorMessage.classList.remove('hidden');
    }

    function hideError() {
        errorMessage.classList.add('hidden');
    }
}