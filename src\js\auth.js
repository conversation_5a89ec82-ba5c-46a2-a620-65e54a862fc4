// Authentication Module
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.users = null;
        this.loadUsers();
    }

    async loadUsers() {
        try {
            const response = await fetch('/src/data/users.json');
            this.users = await response.json();
        } catch (error) {
            console.error('Error loading users:', error);
        }
    }

    async login(username, password, role) {
        if (!this.users) {
            await this.loadUsers();
        }

        let userGroup;
        switch (role) {
            case 'municipal_officer':
                userGroup = this.users.municipal_officers;
                break;
            case 'driver':
                userGroup = this.users.drivers;
                break;
            case 'citizen':
                userGroup = this.users.citizens;
                break;
            default:
                return { success: false, message: 'Invalid role' };
        }

        const user = userGroup.find(u => 
            u.username === username && 
            u.password === password && 
            u.active
        );

        if (user) {
            this.currentUser = { ...user };
            delete this.currentUser.password; // Remove password from session
            localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
            return { success: true, user: this.currentUser };
        } else {
            return { success: false, message: 'Invalid credentials or inactive account' };
        }
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        window.location.href = '/';
    }

    getCurrentUser() {
        if (!this.currentUser) {
            const stored = localStorage.getItem('currentUser');
            if (stored) {
                this.currentUser = JSON.parse(stored);
            }
        }
        return this.currentUser;
    }

    isAuthenticated() {
        return this.getCurrentUser() !== null;
    }

    hasPermission(permission) {
        const user = this.getCurrentUser();
        if (!user) return false;
        
        if (user.role === 'municipal_officer') {
            return user.permissions && user.permissions.includes(permission);
        }
        
        // Drivers and citizens have role-based permissions
        return true;
    }

    requireAuth() {
        if (!this.isAuthenticated()) {
            window.location.href = '/';
            return false;
        }
        return true;
    }

    requireRole(requiredRole) {
        const user = this.getCurrentUser();
        if (!user || user.role !== requiredRole) {
            window.location.href = '/';
            return false;
        }
        return true;
    }
}

// Create global auth instance
window.authManager = new AuthManager();

// Utility functions for role-based routing
function redirectToDashboard(role) {
    switch (role) {
        case 'municipal_officer':
            window.location.href = '/admin-dashboard.html';
            break;
        case 'driver':
            window.location.href = '/driver-dashboard.html';
            break;
        case 'citizen':
            window.location.href = '/citizen-dashboard.html';
            break;
        default:
            window.location.href = '/';
    }
}

// Auto-redirect if already logged in
function checkAuthAndRedirect() {
    const user = window.authManager.getCurrentUser();
    if (user) {
        redirectToDashboard(user.role);
    }
}

export { AuthManager, redirectToDashboard, checkAuthAndRedirect };
