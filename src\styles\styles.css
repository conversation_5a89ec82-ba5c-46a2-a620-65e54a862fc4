@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Mobile-First Styles for Waste Management App */

/* Base responsive utilities */
html {
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Mobile-optimized components */
.btn-mobile {
  min-height: 44px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: colors 0.2s;
}

.btn-mobile-primary {
  background-color: #2563eb;
  color: white;
}

.btn-mobile-primary:hover {
  background-color: #1d4ed8;
}

.btn-mobile-secondary {
  background-color: #e5e7eb;
  color: #111827;
}

.btn-mobile-secondary:hover {
  background-color: #d1d5db;
}

/* Mobile-optimized form inputs */
.input-mobile {
  min-height: 44px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
}

.input-mobile:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Mobile-optimized cards */
.card-mobile {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1rem;
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-specific map container */
.map-mobile {
  height: 16rem;
  width: 100%;
  border-radius: 0.5rem;
}

@media (min-width: 640px) {
  .map-mobile {
    height: 20rem;
  }
}

@media (min-width: 768px) {
  .map-mobile {
    height: 24rem;
  }
}

/* Safe area utilities for mobile devices */
.safe-top {
  padding-top: env(safe-area-inset-top);
}

.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-left {
  padding-left: env(safe-area-inset-left);
}

.safe-right {
  padding-right: env(safe-area-inset-right);
}

/* Mobile-specific visibility */
.mobile-only {
  display: block;
}

.desktop-only {
  display: none;
}

@media (min-width: 640px) {
  .mobile-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }
}

/* Custom animations for mobile interactions */
@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Mobile-specific overrides */
@media (max-width: 640px) {
  /* Ensure minimum touch target sizes */
  button,
  input[type="button"],
  input[type="submit"],
  .btn {
    min-height: 44px;
    min-width: 44px;
  }

  /* Optimize table display for mobile */
  .table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-specific map controls */
  .leaflet-control-container {
    font-size: 14px;
  }

  .leaflet-control-zoom a {
    width: 36px;
    height: 36px;
    line-height: 36px;
  }
}