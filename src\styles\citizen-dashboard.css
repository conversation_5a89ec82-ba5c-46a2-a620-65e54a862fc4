:root {
            --primary: #2e7d32;
            --primary-dark: #1b5e20;
            --primary-light: #4caf50;
            --secondary: #ff9800;
            --accent: #f44336;
            --light: #f5f5f5;
            --dark: #263238;
            --gray: #78909c;
            --success: #4caf50;
            --warning: #ff9800;
            --danger: #f44336;
            --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --transition: all 0.3s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f0f4f8;
            color: var(--dark);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* Header Styles */
        header {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .logo-icon {
            background-color: rgba(255, 255, 255, 0.2);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo-text {
            font-size: 1.25rem;
            font-weight: 600;
        }
        
        .user-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-info {
            font-size: 0.9rem;
            display: none;
        }
        
        .logout-btn {
            background-color: rgba(255, 255, 255, 0.15);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: var(--transition);
        }
        
        .logout-btn:hover {
            background-color: rgba(255, 255, 255, 0.25);
        }
        
        /* Main Content */
        main {
            flex: 1;
            padding: 1.5rem;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
        
        @media (min-width: 768px) {
            .dashboard-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* Status Card */
        .status-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
        }
        
        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .status-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .status-value {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary);
        }
        
        .bin-status {
            background-color: #e8f5e9;
            color: var(--success);
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        @media (min-width: 640px) {
            .quick-actions {
                grid-template-columns: repeat(4, 1fr);
            }
        }
        
        .action-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            border-color: var(--primary-light);
        }
        
        .action-card.report {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: var(--danger);
        }
        
        .action-card.request {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #1976d2;
        }
        
        .action-card.schedule {
            background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
            color: var(--success);
        }
        
        .action-card.education {
            background: linear-gradient(135deg, #fff8e1, #ffecb3);
            color: #f57c00;
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }
        
        .action-text {
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        /* Content Cards */
        .content-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            transition: var(--transition);
        }
        
        .content-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-dark);
        }
        
        .view-all {
            font-size: 0.9rem;
            color: var(--primary);
            font-weight: 500;
            cursor: pointer;
        }
        
        /* Schedule Calendar */
        .schedule-calendar {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }
        
        .schedule-day {
            background-color: #e8f5e9;
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            transition: var(--transition);
        }
        
        .schedule-day:hover {
            background-color: #c8e6c9;
            transform: scale(1.03);
        }
        
        .schedule-day.active {
            background-color: var(--primary);
            color: white;
        }
        
        .day-name {
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .day-date {
            font-size: 1.25rem;
            font-weight: 700;
            margin: 0.5rem 0;
        }
        
        .day-type {
            font-size: 0.85rem;
            background-color: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.5rem;
            border-radius: 50px;
            display: inline-block;
        }
        
        /* Complaints List */
        .complaints-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .complaint-item {
            background-color: #f5f5f5;
            border-radius: 12px;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
        }
        
        .complaint-item:hover {
            background-color: #eeeeee;
        }
        
        .complaint-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }
        
        .complaint-title {
            font-weight: 500;
        }
        
        .complaint-date {
            font-size: 0.85rem;
            color: var(--gray);
        }
        
        .complaint-status {
            padding: 0.25rem 0.75rem;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .status-pending {
            background-color: #fff8e1;
            color: #f57c00;
        }
        
        .status-resolved {
            background-color: #e8f5e9;
            color: var(--success);
        }
        
        /* Waste Guide */
        .waste-guide {
            margin-top: 1.5rem;
        }
        
        .guide-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 1rem;
        }
        
        @media (min-width: 640px) {
            .guide-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        .guide-card {
            border-radius: 16px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            transition: var(--transition);
            box-shadow: var(--card-shadow);
        }
        
        .guide-card:hover {
            transform: translateY(-5px);
        }
        
        .guide-card.organic {
            background: linear-gradient(135deg, #e8f5e9, #c8e6c9);
        }
        
        .guide-card.recyclable {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
        }
        
        .guide-card.general {
            background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
        }
        
        .guide-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .guide-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .guide-icon.organic {
            background-color: var(--success);
        }
        
        .guide-icon.recyclable {
            background-color: #1976d2;
        }
        
        .guide-icon.general {
            background-color: #78909c;
        }
        
        .guide-title {
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .guide-content {
            font-size: 0.95rem;
            line-height: 1.5;
        }
        
        /* Footer */
        footer {
            background-color: var(--dark);
            color: white;
            padding: 2rem 1rem;
            margin-top: 2rem;
        }
        
        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 2rem;
        }
        
        @media (min-width: 768px) {
            .footer-container {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        .footer-section h3 {
            font-size: 1.25rem;
            margin-bottom: 1rem;
            color: var(--primary-light);
        }
        
        .footer-links {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .footer-links a {
            color: #cfd8dc;
            text-decoration: none;
            transition: var(--transition);
        }
        
        .footer-links a:hover {
            color: white;
        }
        
        .contact-info {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #cfd8dc;
        }
        
        .social-links {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .social-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition);
        }
        
        .social-icon:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        
        .copyright {
            text-align: center;
            padding-top: 2rem;
            color: #90a4ae;
            font-size: 0.9rem;
        }
        
        /* Modals */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }
        
        .modal-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .modal {
            background-color: white;
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transform: translateY(20px);
            transition: var(--transition);
        }
        
        .modal-overlay.active .modal {
            transform: translateY(0);
        }
        
        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-dark);
        }
        
        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--gray);
        }
        
        .modal-body {
            padding: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            transition: var(--transition);
        }
        
        .form-control:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            font-family: 'Poppins', sans-serif;
        }
        
        .btn-secondary {
            background-color: #f5f5f5;
            color: var(--dark);
        }
        
        .btn-secondary:hover {
            background-color: #e0e0e0;
        }
        
        .btn-primary {
            background-color: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background-color: var(--primary-dark);
        }
        
        /* Responsive Adjustments */
        @media (max-width: 640px) {
            .header-container {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }
            
            .user-actions {
                align-self: flex-end;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .status-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }
            
            .schedule-calendar {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        /* Animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-in {
            animation: fadeIn 0.5s ease forwards;
        }
        
        .delay-1 { animation-delay: 0.1s; }
        .delay-2 { animation-delay: 0.2s; }
        .delay-3 { animation-delay: 0.3s; }
        .delay-4 { animation-delay: 0.4s; }